<?php

/**
 * Class Class_horses.
 */
class Class_horses extends Racing_Emporium_API
{
	/**
	 * Class_horses constructor.
	 */
	function __construct($plugin_name, $version)
	{
		parent::__construct($plugin_name, $version);
	}

	/**
	 * Horses List.
	 */
	public function index($request)
	{
		try {

			$args = [
				'status'     => ['publish'],
				'category' 	 => ['racehorses'],
				'visibility' => 'visible',
				'tax_query'  => [
					'taxonomy' => 'product_visibility',
					'terms'    => ['exclude-from-catalog'],
					'field'    => 'name',
					'operator' => 'NOT IN',
				],
			];

			# TOTAL RECORDS
			$data['total_records'] = count(wc_get_products($args));

			# IF LIMIT IS SET
			$args['numberposts'] = -1;
			$data['total_pages'] = 1;
			if ($request->get_param('limit')) {
				$args['numberposts'] = $request->get_param('limit');
				$data['total_pages'] = ceil($data['total_records'] / $request->get_param('limit'));
			}

			# IF PAGE IS SET
			if ($request->get_param('page')) {
				$args['page'] = $request->get_param('page');
			}

			# GET HORSES
			$horses = wc_get_products($args);

			$data['rows'] = [];
			foreach ($horses as $horse) {
				$share_end_date = get_field('share_endrenewal_date', $horse->get_id());

				$data['rows'][] = [
					'id' 	  				 => (string)$horse->get_id(),
					'name'  				 => (string)$horse->get_name(),
					'image' 				 => (string)wp_get_attachment_image_url($horse->get_image_id(), 'full'),
					'horse_age' 		 => (string)get_field('horses_age', $horse->get_id()),
					'share_end_date' => ($share_end_date != '') ? DateTime::createFromFormat('m/d/Y', $share_end_date)->format('Y-m-d') : '',
					'description' 	 => (string)get_field('horse_breif_description', $horse->get_id()),
					'price'		  		 => (string)$horse->get_price(),
					'stock_status'	 => ($horse->get_stock_status() != 'outofstock') ? true : false,
				];
			}

			return parent::returnResponseWithData(__('Horses retrieved.', 'racing-emporium'), $data);
		} catch (Throwable $e) {

			return parent::returnError(__('Something went wrong.', 'racing-emporium'), $e);
		}
	}

	/**
	 * Horse Detail API.
	 */
	public function detail($request)
	{
		try {

			# GET PRODUCT
			$product = wc_get_product($request['id']);

			# GET PRODUCT IMAGE
			$images[]['url'] = (string)wp_get_attachment_image_url($product->get_image_id(), 'full');
			$attachments = $product->get_gallery_image_ids();
			if (!empty($attachments)) {
				foreach ($attachments as $attachment) {
					$images[]['url'] = wp_get_attachment_url($attachment);
				}
			}

			$share_end_date = get_field('share_endrenewal_date', $product->get_ID());

			$data = [
				'id' 	 							=> (string)$product->get_ID(),
				'name' 							=> (string)$product->get_name(),
				'short_description' => (string)strip_tags($product->get_short_description()),
				'long_description'  => (string)wpautop($product->get_description()),
				'share_end_date' 	  => ($share_end_date != '') ? DateTime::createFromFormat('m/d/Y', $share_end_date)->format('Y-m-d') : '',
				'price'							=> (string)$product->get_price(),
				'stock_status'			=> ($product->get_stock_status() != 'outofstock') ? true : false,
				'images'						=> $images,
				'trainer'						=> [
					'name'  => (string)get_field('trainer_name', $product->get_ID()),
					'image' => (string)get_field('trainer_image', $product->get_ID())['url'],
				],
			];

			return parent::returnResponseWithData(__('Horse detail retrieved.', 'racing-emporium'), $data);
		} catch (Throwable $e) {

			return parent::returnError(__('Something went wrong.', 'racing-emporium'), $e);
		}
	}

	/**
	 * Galley API.
	 */
	public function gallery($request)
	{
		try {

			# FIND HORSE
			$horse = wc_get_product($request['id']);

			# FIND IMAGES
			$images      = [];
			if (get_field('product_downloads_images', $horse->get_ID())) {
				while (the_repeater_field('product_downloads_images', $horse->get_ID())) {
					$images[] = [
						'url'  => (string)get_sub_field('product_downloads_images_media_image')['url'],
						'name' => (string)get_sub_field('product_downloads_images_title'),
					];
				}
			}

			# FIND VIDEOS
			$videos  = [];
			if (get_field('product_downloads_videos', $horse->get_ID())) {
				while (the_repeater_field('product_downloads_videos', $horse->get_ID())) {
					$videos[] = [
						'url' 				=> (string)get_sub_field('product_downloads_videos_youtube_link'),
						'title' 			=> (string)get_sub_field('product_downloads_videos_title'),
						'description' => (string)get_sub_field('product_downloads_videos_description'),
					];
				}
			}

			# FIND DOCUMENTS
			$documents  = [];
			if (get_field('product_downloads_documents', $horse->get_ID())) {
				while (the_repeater_field('product_downloads_documents', $horse->get_ID())) {
					$documents[] = [
						'url' 	=> (string)get_sub_field('product_downloads_documents_media_document')['url'],
						'title' => (string)get_sub_field('product_downloads_documents_title'),
						'date'  => (string)get_sub_field('product_downloads_documents_document_date'),
					];
				}
			}

			return parent::returnResponseWithData(__('Horse retrieved.', 'racing-emporium'), [
				'images' 		=> $images,
				'videos' 		=> $videos,
				'documents' => $documents,
			]);
		} catch (Throwable $e) {

			return parent::returnError(__('Something went wrong.', 'racing-emporium'), $e);
		}
	}
}
