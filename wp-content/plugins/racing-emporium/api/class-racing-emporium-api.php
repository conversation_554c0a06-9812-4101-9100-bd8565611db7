<?php

use Firebase\JWT\JWT;

use Square\SquareClient;
use Square\Environment;
use Square\ConfigurationInterface;
use Square\Exceptions\ApiException;

// use Square\SquareClient;
// use Square\Models\SearchLoyaltyAccountsRequest;
// use Square\Environment;
// use Square\Server;

/**
 * Classs Racing_Emporium_API.
 */
class Racing_Emporium_API
{

  /**
   * string $plugin_name
   */
  public $plugin_name;

  /**
   * string $version
   */
  public $version;

  /**
   * string $jwt_secret_key
   */
  public $jwt_secret_key;

  /**
   * string $fcm_tokens_table
   */
  private $jwt_tokens_table;

  /**
   * Class Racing_Emporium_API constructor.
   */
  public function __construct($plugin_name, $version)
  {
    global $table_prefix;

    $this->plugin_name = $plugin_name;

    $this->version = $version;

    $this->jwt_secret_key = defined('JWT_AUTH_SECRET_KEY') ? JWT_AUTH_SECRET_KEY : false;

    $this->jwt_tokens_table = $table_prefix . 'racing_jwt_tokens';

    $this->create_tables();

    if (!$this->jwt_secret_key) {
      return $this->returnError(__('APP not configurated properly.', 'wp-api-jwt-auth'));
    }

    $this->braintreeObj = new Braintree_Gateway([
      'environment'   => 'sandbox',
      'merchantId'     => 'dcj5d6grpzmbw499',
      'publicKey'     => 'jpnsqjsfvjfs2hws',
      'privateKey'     => 'b77f92aa31031142a02b614a5b8dd20d'
    ]);

    // $client = new SquareClient([
    //   'accessToken' => '****************************************************************',
    //   'environment' => 'sandbox',
    // ]);

  }

  /**
   * Creates neccessary tables if not exists.
   */
  public function create_tables()
  {
    global $table_prefix, $wpdb;

    if (!function_exists('dbDelta')) {
      require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    }

    # CHECK IF JWT TOKENS TABLES EXISTS.
    if ($wpdb->get_var("SHOW TABLES LIKE '" . $this->jwt_tokens_table . "'") != $this->jwt_tokens_table) {
      dbDelta("CREATE TABLE IF NOT EXISTS `" . $this->jwt_tokens_table . "` (
        `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        `user_id` BIGINT(20) UNSIGNED NOT NULL,
        `token` LONGTEXT NOT NULL,
        `jti` LONGTEXT NOT NULL,
        `logged_out` TINYINT(1) NOT NULL DEFAULT 0,
        `created_at` DATETIME,
        `updated_at` DATETIME,
        PRIMARY KEY (`id`),
        INDEX `user_id` (`user_id` ASC),
        CONSTRAINT `" . $this->jwt_tokens_table . "_ibfk_1`
          FOREIGN KEY (`user_id`)
          REFERENCES `wp_users` (`ID`))
      ENGINE = InnoDB
      DEFAULT CHARACTER SET = utf8");
    }
  }

  /**
   * @hook rest_api_init.
   */
  public function add_api_routes()
  {
    $routes = require plugin_dir_path(dirname(__FILE__)) . 'api/routes.php';

    foreach ($routes as $namespace => $route) {
      require plugin_dir_path(dirname(__FILE__)) . 'api/class-' . $namespace . '.php';
      $class  = 'Class_' . str_replace('-', '_', $namespace);
      $object = new $class($this->plugin_name, $this->version);

      foreach ($route as $endpoint) {
        register_rest_route('v1', '/' . $namespace . '/' . $endpoint['name'], [
          'methods'  => $endpoint['method'],
          'permission_callback' => '__return_true',
          'callback' => function ($request) use ($object, $endpoint) {

            if (isset($endpoint['middleware'])) {
              if (in_array('auth', $endpoint['middleware'])) {
                if (!$this->logged_in_user()) {
                  return $this->returnError(__('Unauthorized!', 'racing-emporium'), $this->logged_in_user(true), 401);
                }
              }

              if (in_array('currency', $endpoint['middleware'])) {
                global $WOOCS;

                # SET DEFAULT CURRENCIES
                $current_currency = $request->get_param('currency');
                // $currencies = array_keys( $WOOCS->get_currencies() );
                // write_log($currencies);
                # CURRENCY IS REQUIRED
                if (!$current_currency) {
                  return $this->returnError(__('Currency is required.', 'racing-emporium'));
                }

                // # INVALID CURRENCY
                // if( !in_array( $current_currency, $currencies ) )
                // {
                //   return $this->returnError( __('Invalid currency.', 'racing-emporium' ) );
                // }

                # SET CURRENCY
                //$WOOCS->set_currency( $current_currency );
              } else {
                global $WOOCS;
                // $WOOCS->reset_currency();
              }
            }

            return $object->{$endpoint['handler']}($request);
          },
        ]);
      }
    }
  }

  /**
   * @hook rest_api_init.
   */
  public function add_cors_support()
  {
    $enable_cors = defined('JWT_AUTH_CORS_ENABLE') ? JWT_AUTH_CORS_ENABLE : false;
    if ($enable_cors) {
      $headers = apply_filters('jwt_auth_cors_allow_headers', 'Access-Control-Allow-Headers, Content-Type, Authorization');
      header(sprintf('Access-Control-Allow-Headers: %s', $headers));
    }
  }

  /**
   * Generates JWY Token.
   */
  public function generateToken($user_id)
  {
    global $wpdb;

    $user = get_user_by('ID', $user_id);
    $jti  = substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'), 1, 25) . '-' . time();

    $token = JWT::encode([
      'iss' => get_bloginfo('url'),
      'sub' => 'api_token',
      'aud' => 'user',
      'exp' => null,
      'nbf' => time() - 10,
      'iat' => time(),
      'jti' => $jti,
      'data' => [
        'user' => $user,
      ],
    ], $this->jwt_secret_key);

    $wpdb->insert($this->jwt_tokens_table, [
      'user_id'    => $user_id,
      'token'      => $token,
      'jti'        => $jti,
      'logged_out' => 0,
      'created_at' => date('Y-m-d H:i:s'),
      'updated_at' => date('Y-m-d H:i:s'),
    ]);

    return $token;
  }

  /**
   * Verify token.
   */
  public function logged_in_user($returnError = false)
  {
    global $wpdb;

    if (get_current_user_id()) {
      return wp_get_current_user();
    } else if ($_SERVER['REQUEST_METHOD'] == 'PUT') {
      parse_str(file_get_contents('php://input'), $input);
      if (isset($input['_wpnonce'])) {
        echo (int)wp_verify_nonce($input['_wpnonce'], 'wp_rest');
      }
    }

    $auth_header = (isset($_SERVER['HTTP_AUTHORIZATION'])) ?  $_SERVER['HTTP_AUTHORIZATION'] : false;
    if (!$auth_header) {
      $auth_header = (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) ? $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] : false;
    }

    # CHECK AUTHORIZATION ERROR
    if (!$auth_header) {
      return ($returnError) ? __('Authorization header not found.', 'racing-emporium') : false;
    }

    try {

      # DECODE TOKEN
      $token = JWT::decode($auth_header, $this->jwt_secret_key, array('HS256'));

      # CHECK IF TOKEN MATCH WITH SERVER
      if ($token->iss != get_bloginfo('url')) {
        return ($returnError) ? __('The token does not match with server.') : false;
      }

      # CHECK IF VALID USER
      if (!isset($token->jti) || !isset($token->data) || !isset($token->data->user) || !isset($token->data->user->ID)) {
        return ($returnError) ? __('Invalid jti or user not found.') : false;
      }

      # FIND USER
      $user = get_user_by('ID', $token->data->user->ID);

      # VALIDATE TOKEN
      $jwt_token = $wpdb->get_row($wpdb->prepare("SELECT * FROM `" . $this->jwt_tokens_table . "` WHERE
      `" . $this->jwt_tokens_table . "`.`user_id` = %d AND
      `" . $this->jwt_tokens_table . "`.`jti` =  %s AND 
      `" . $this->jwt_tokens_table . "`.`logged_out` = '0'", [
        $token->data->user->ID,
        $token->jti,
      ]));

      if (!$user || empty($user) || empty($jwt_token)) {
        return ($returnError) ? __('You\'ve been logged out from current session of user.') : false;
      }

      return $user;
    } catch (Exception $e) {

      if ($returnError) {
        return ($e && method_exists($e, 'getMessage')) ? (string)$e->getMessage() : ((is_string($e)) ? (string)$e : 'Invalid Token.');
      }

      return false;
    }
  }

  /**
   * Delete token.
   */
  public function user_logout($user_id, $token)
  {
    global $wpdb;

    $auth_header = (isset($_SERVER['HTTP_AUTHORIZATION'])) ?  $_SERVER['HTTP_AUTHORIZATION'] : false;
    if (!$auth_header) {
      $auth_header = (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) ? $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] : false;
    }

    # CHECK AUTHORIZATION ERROR
    if (!$auth_header) {
      return ($returnError) ? __('Authorization header not found.', 'racing-emporium') : false;
    }

    try {

      # DELETE TOKEN
      $wpdb->delete($this->jwt_tokens_table, [
        'user_id'     => $user_id,
        'token'   => $token,
      ]);
      return true;
    } catch (Exception $e) {

      return false;
    }
  }

  /**
   * Return error.
   */
  public function returnError($message, $e = '', $code = 400)
  {
    $response = [
      'message' => (string)$message,
      'error'   => ($e && method_exists($e, 'getMessage')) ? (string)$e->getMessage() : ((is_string($e)) ? (string)$e : ''),
    ];
    return new WP_REST_Response($response, $code);
  }

  /**
   * Return success response with data.
   */
  public function returnResponseWithData($message, $data)
  {
    $response = [
      'message' => $message,
      'data' => $data
    ];
    return new WP_REST_Response($response, 200);
  }

  /**
   * Return success response without data.
   */
  public function returnResponse($message)
  {
    $response = [
      'message' => $message
    ];
    return new WP_REST_Response($response, 200);
  }
}
